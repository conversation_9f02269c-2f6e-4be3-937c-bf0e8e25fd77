"""
Call Response

Input (Last Sumamry + Latest Question)

Calculate: Query+Answer(done), Product_Identify(static), Ongoing_or_first_convo, <PERSON><PERSON>(done), <PERSON>(done), Personality(done)

Calculate: Refine_Reply
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Literal
from langchain_core.runnables import RunnableMap
from src.core.database import get_admin_db
from src.models.summary import SummaryModel
from src.helper.logger import setup_new_logging
from src.v2.dashboard.cta.models import CTAType
import streamlit as st

from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.core.llms import ChatMessage
# from src.helper.resolve_llm import resolve_llm

from src.streamlitdemo.chatdemo.gen_query_n_reply import generate_answer_from_query, generate_query
from src.streamlitdemo.chatdemo.senti_lang import  detect_language 
from src.streamlitdemo.chatdemo.identify_product import identify_product
from src.streamlitdemo.chatdemo.simulate_tools import (
    # get_available_booking_dates,
    # book_date,
    # get_current_date,
    # check_booking_status,
    create_issue_tickets,
    # get_ticket_status,
    initial_address_information,
    handle_booking,
)

# from src.streamlitdemo.chatdemo.get_db import db
from src.helper.resolve_llm import resolve_llm
# from src.streamlitdemo.chatdemo.track_time import time_it
import openai
import json  # Add this import at the top
import asyncio
from fastmcp import Client
              
logger = setup_new_logging(__name__)

class MsgRequest(BaseModel):
    customer_id: Optional[str] = None
    chat_history_format: Literal["role_content", "role_data"] = "role_content"
    chat_history: Optional[List] = None
    message: Optional[str] = None
    message_media_values: Optional[str] = None
    image_process_metadata: Optional[Dict] = None
    suggested_product:Optional[List]=None
    current_spin_state: Optional[str] = None
    next_spin_state: Optional[str] = None
    info_gathering: Optional[dict] = None
    primary_product_code: Optional[str] = None
    primary_product: Optional[str] = None

    previous_summary: Optional[str | list] = None
    spin_states: Optional[Dict] = None
    channel: Optional[str] = None


    #let's just use message and user_id from this

    @property
    def json_data_with_fields_str_formatted(self) -> str:
        gathered_info = self.info_gathering
        gathered_info.pop("name")
        return str(gathered_info)
    def set_new_summary(self, summary_str):
        self.previous_summary = summary_str

async def generate_response_openai(req: MsgRequest, SYS_PROMPT, qd_client, current_user) -> str:
    print("chatdemo response openai")
    SYSTEM_PROMPT = SYS_PROMPT["text"]
    primary_product_code=req.primary_product_code
    primary_product=req.primary_product

    source_nodes = []
    tool_calls = None
    tool_calls_results = []
    identify_result = None  
    suggested_products = []  

    DETECT_LANGUAGE_PROMPT = current_user.db.prompt.find_one({"name":"language_detection"})
 
    IDENTIFY_PRODUCT_PROMPT = current_user.db.prompt.find_one({"name":"identify_product"})


    # Run language detection and product identification concurrently for better performance
    identify_task = asyncio.create_task(
        asyncio.to_thread(identify_product, {"latest_message_str": req.message, "prompt": IDENTIFY_PRODUCT_PROMPT}, current_user=current_user)
    )
    language_task = asyncio.create_task(
        asyncio.to_thread(detect_language, {"latest_message_str": req.message, "prompt": DETECT_LANGUAGE_PROMPT}, current_user=current_user)
    )

    identified_product, prod_usage = await identify_task
    language_response = await language_task
    # print("\n\n language_response",language_response)

       # Extract language from new format
    if isinstance(language_response, dict) and "result" in language_response:
        language = language_response["result"].get("detected_language", None)
        lang_usage = language_response.get("usage", {})
    else:
        language, lang_usage = None, {}

    org_detail=current_user.db.business_info.find_one({"name":"BusinessInfo"})
    if not org_detail:
        raise Exception("Business Detail not found. Complete the Setup ")
    

    prefered_language = org_detail.get("language", [])
    if not isinstance(prefered_language, list):
        prefered_language = [prefered_language]

    # Decision logic
    if "Auto Detect" in prefered_language and language:
        language_to_reply = language
    elif language in prefered_language:
        language_to_reply = language
    elif prefered_language:
        language_to_reply = prefered_language[0]
    else:
        language_to_reply = "english"

    print("language_to_replyyy",language_to_reply)

    ticket_required_info = org_detail.get("ticket_required_info", [])

    # Filter out 'description' and 'issue_type'
    filtered_info = [field for field in ticket_required_info if field not in ["_description", "_issue_type","description","issue_type"]]
    ticket_required_prompt=""
    booking_required_prompt=""
    greeting_prompt=""
    user_requirements=get_admin_db().prompt.find_one({"name":"user_requirement_prompt_setup"})

    if filtered_info:
        prompt_template = user_requirements.get("ticket_required_prompt", "")
        fields = ", ".join([f"{field}" for field in filtered_info])
        ticket_required_prompt = prompt_template.format(fields=fields)
        # ticket_required_prompt = prompt_template.format(fields=", ".join(filtered_info))

    AI_greeting = org_detail.get("AI_greeting", False)
    if AI_greeting:
        greeting_prompt= user_requirements.get("greeting_prompt","")
    booking = org_detail.get("booking", None)
    booking_required_info = org_detail.get("booking_required_info", [])
    if booking =="handle_booking":
        prompt_template = user_requirements.get("booking_required_prompt", "")
        filtered_booking_req = [field for field in booking_required_info if field not in ["query","description"]]
        fields = ", ".join([f"{field}" for field in filtered_booking_req])
        booking_required_prompt = prompt_template.format(fields=fields)


    # Format system prompt
    SYSTEM_PROMPT = SYSTEM_PROMPT.format(
        org_name=org_detail.get("org_name"),
        org_goal=org_detail.get("org_goal"),
        business_type=org_detail.get("org_type"),
        agent_name=org_detail.get("agent_name"),
        agent_role=org_detail.get("agent_goal"),
        org_description=org_detail.get("org_description"),
        language_to_reply=language_to_reply,
        ticket_required_prompt=ticket_required_prompt,
        booking_required_prompt=booking_required_prompt,
        greeting_prompt=greeting_prompt,
        primary_product_code=primary_product_code,
        primary_product = primary_product,
        


        # answer=data["query_answer"].get("reply"),
        identified_product=identified_product,
        language=language,
        # additional_context=data.get("additional_context", ""),
        response_mode_str="Provide a detailed and explained answer in a couple of sentences."


    )

    env = current_user.db.settings.find_one({"name": "env"})
    API_KEYS = env.get("config")
    openai_client = openai.OpenAI(api_key=API_KEYS.get("OPENAI_API_KEY"))

    print(SYSTEM_PROMPT)

    # Initialize messages list
    messages = [{"role": "system", "content": SYSTEM_PROMPT}]
    if isinstance(req.previous_summary, list):
        messages.extend(req.previous_summary)
    messages.append({"role": "user", "content": req.message})

    if req.message_media_values:
        logger.debug(f"Message Media Values: {req.message_media_values}")
        messages.append({"role": "user", "content": f"Description of User Provided Images:\n{req.message_media_values}"})

    # Define tools
    # Load all enabled tools
    tools_cursor = current_user.db["tools"].find({"is_enabled": True})
    tools=[]

    for tool in tools_cursor:
        tools.append(tool["tools"][0])
    
    if not tools:
        raise Exception("No tools found")


    # Initial API call
    response = openai_client.chat.completions.create(
        model=SYS_PROMPT["model"],
        messages=messages,
        tools=tools,
        tool_choice="auto",
        parallel_tool_calls=True,
    )
    original_usage ={"model":SYS_PROMPT["model"], **response.usage.model_dump()}

    # Allow multiple rounds of tool calls
    max_tool_calls = 5  # Prevent infinite loops
    current_calls = 0
    token_ussage={}
    while current_calls < max_tool_calls:
        current_calls += 1
        assistant_message = response.choices[0].message
        messages.append(assistant_message)

        if not assistant_message.tool_calls:
            break

        tool_calls = assistant_message.tool_calls

        # Handle all tool calls in this round
        for tool_call in assistant_message.tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)
            print("function_args",function_args)
            tool_call_id = tool_call.id  # Store the tool call ID

            # Execute the appropriate function
            if function_name == "search_database":
                # print(f"Round {current_calls} - FUNCTION ARGS for search: {function_args}")
                result, source_nodes = await asyncio.to_thread(
                    generate_answer_from_query,
                    query=function_args["query"],
                    qd_client=qd_client,
                    current_user=current_user
                )
                source_nodes = source_nodes

            elif function_name == "initial_address_information":
                # print(f"Round {current_calls} - FUNCTION ARGS for initial: {function_args}")
                result = await asyncio.to_thread(initial_address_information, db=current_user.db)

            elif function_name == "create_issue_tickets":
                print(f"Round {current_calls} - FUNCTION ARGS for create: {function_args}")
                
                # Get the tool definition to check required fields
                ticket_tool = None
                for tool in tools:
                    if tool.get("type") == "function" and tool.get("function", {}).get("name") == "create_issue_tickets":
                        ticket_tool = tool
                        break

                # Extract required fields from the tool definition
                required_fields = []
                if ticket_tool:
                    required_fields = ticket_tool.get("function", {}).get("parameters", {}).get("required", [])
                    print(f"Required fields from tool definition: {required_fields}")
                else:
                    print("WARNING: ticket_tool not found in tools definition!")

                # Define placeholder values that should be treated as missing
                placeholder_values = {
                    "not provided", "n/a", "none", "null", "undefined", "",
                    "not available", "na", "nil", "empty", "-", "0",
                    "missing", "unknown", "tbd", "to be determined"
                }

                def is_field_valid(value, field_name=None):
                    """Check if a value is valid (not empty or placeholder)"""
                    if value is None:
                        return False
                    if isinstance(value, str):
                        value = value.lower().strip()
                        if value in placeholder_values or not bool(value):
                            return False
                    return True

                # Validate required fields
                missing_fields = [field for field in required_fields if not is_field_valid(function_args.get(field), field)]
                
                if missing_fields:
                    print(f"VALIDATION FAILED - Missing required fields for ticket: {missing_fields}")
                    result = {
                        "error": f"Missing required fields for ticket: {', '.join(missing_fields)}",
                        "missing_fields": missing_fields
                    }
                else:
                    # Set default issue_type if not provided
                    if not function_args.get("issue_type"):
                        print("Issue type not provided, defaulting to ticket")
                        function_args["issue_type"] = CTAType.TICKET.value

                    # Prepare extra fields (all non-standard fields)
                    # extra_fields = {k: v for k, v in function_args.items() 
                    #             if k not in ["issue_type", "description"]}
                                
                    
                    try:
                        print("VALIDATION PASSED - Creating ticket")
                        result = await asyncio.to_thread(
                                create_issue_tickets,
                                user_req=req,
                                channel=req.channel,
                                current_user=current_user,
                                **function_args
                            )

                        function_args["cta_id"] = result["cta_id"]
                    except Exception as e:
                        print(f"Error in create_issue_tickets: {e}")
                        result = {"error": f"Error in create_issue_tickets: {str(e)}"}
            elif function_name == "handle_booking":
                print(f"Round {current_calls} - FUNCTION ARGS for booking: {function_args}")
                
                # Get the tool definition to check required fields
                booking_tool = None
                for tool in tools:
                    if tool.get("type") == "function" and tool.get("function", {}).get("name") == "handle_booking":
                        booking_tool = tool
                        break

                # Extract required fields from the tool definition
                required_fields = []
                if booking_tool:
                    required_fields = booking_tool.get("function", {}).get("parameters", {}).get("required", [])
                    print(f"Required fields from tool definition: {required_fields}")
                else:
                    print("WARNING: booking_tool not found in tools definition!")

                # Define placeholder values that should be treated as missing
                placeholder_values = {
                    "not provided", "n/a", "none", "null", "undefined", "",
                    "not available", "na", "nil", "empty", "-", "0",
                    "missing", "unknown", "tbd", "to be determined"
                }

                def is_field_valid(value, field_name=None):
                    """Check if a value is valid (not empty or placeholder)"""
                    # Exception fields that are always considered valid (optional fields)
                    optional_fields = {"customer_medical_history", "intrested_topic", "customer_age"}
                    
                    # If it's an optional field, always return True (allow any value including empty/placeholder)
                    if field_name and field_name in optional_fields:
                        return True
                        
                    if value is None:
                        return False
                    if isinstance(value, str):
                        value = value.lower().strip()
                        if value in placeholder_values or not bool(value):
                            return False
                    return True

                # Validate required fields
                missing_fields = [field for field in required_fields if not is_field_valid(function_args.get(field), field)]
                
                if missing_fields:
                    print(f"VALIDATION FAILED - Missing required fields for booking: {missing_fields}")
                    result = {
                        "error": f"Missing required fields for booking: {', '.join(missing_fields)}",
                        "missing_fields": missing_fields
                    }
                else:
                    # Set default issue_type if not provided
                    if not function_args.get("issue_type"):
                        print("Issue type not provided, defaulting to booking")
                        function_args["issue_type"] = CTAType.BOOKING.value

                    # Prepare booking_args with all valid fields
                    booking_args = {k: v for k, v in function_args.items() 
                                if is_field_valid(v, k) or k in required_fields}
                    
                    try:
                        print("VALIDATION PASSED - Creating booking")
                        result, data = await asyncio.to_thread(
                            handle_booking,
                            **booking_args,
                            user_req=req,
                            channel=req.channel,
                            current_user=current_user
                        )
                        function_args["cta_id"] = data["cta_id"]
                        function_args["issue_type"] = CTAType.BOOKING.value
                    except Exception as e:
                        print(f"Error in handle_booking: {e}")
                        result = {"error": f"Error in handle_booking: {str(e)}"}
            elif function_name == "search_product_names":
                print("search_product_names called")
                print("Initial function_args:", function_args)

                args = {
                    "latest_message_str": function_args.get("query")
                }

                try:
                    # http://172.16.16.155:8686/sse===http://127.0.0.1:8686/
                    async with Client("http://172.16.16.155:8686/sse", timeout=30) as client:
                        call_result = await client.call_tool(
                            "search_products",
                            args,
                            raise_on_error=False
                        )
                        print("call_resulttt", call_result)

                        product_list = []
                        result={}

                        # if call_result.is_error:
                        #     result = {
                        #         "products": [f"No product found for {function_args.get('query')}"],
                        #         # "query": args["latest_message_str"],
                        #         "error": str(call_result.content)
                        #     }

                        if call_result.content and len(call_result.content) > 0:
                            try:
                                search_content = json.loads(call_result.content[0].text)

                                raw_product_list = search_content.get("product", [])
                                

                                # Optional: check if it’s an error response inside JSON
                                if search_content.get("status") == "400" or (
                                    isinstance(raw_product_list, list) and 
                                    len(raw_product_list) > 0 and 
                                    isinstance(raw_product_list[0], dict) and 
                                    "error" in raw_product_list[0]
                                ):
                                    print("called resss")
                                    result = {
                                        "products": [f"No product found for {function_args.get('query')}"],
                                        "query": args["latest_message_str"],
                                        "error": raw_product_list[0].get("error", "Unknown error")
                                    }
                                else:
                                    print("called ressss111ss")
                                    for item in raw_product_list:
                                        product_id = (
                                            item.get("Product Code") or item.get("Product_code") or item.get("ProductCode")
                                        )
                                        product_name = (
                                            item.get("Product Name") or item.get("Product_name") or item.get("Product_Name")
                                        )
                                        product_price = item.get("price")

                                        if product_id and product_name:
                                            product_list.append({
                                                "id": product_id,
                                                "name": product_name,
                                                "price": product_price
                                            })

                                    result = {
                                        "products": product_list,
                                        "query": args["latest_message_str"]
                                    }

                            except json.JSONDecodeError as e:
                                result = {
                                    "products": [f"No product found for {function_args.get('query')}"],
                                    # "query": args["latest_message_str"],
                                    "error": "Invalid response format"
                                }

                        else:
                            result = {
                                "products": [f"No product found for {function_args.get('query')}"],
                                # "query": args["latest_message_str"],
                                "error": "Empty content from tool"
                            }

                except Exception as e:
                    print(f"Tool call failed: {str(e)}")
                    result = {
                        "products": [f"No product found for {function_args.get('query')}"],
                        # "query": args["latest_message_str"],
                        "error": str(e)
                    }


            elif function_name == "assign_product":
                # print("dddddddddddddddddddddddddddddd",product_list, [])
                
                print("Calling assign_product with:", args)
                print("Calling assign_product test:", function_args.get("product_codes"))
                suggested_products=function_args.get("product_codes")
                result={"suggested_products":suggested_products}
                 

            else:
                result = {"error": f"Unknown function: {function_name}"}

            # Always add the result to tool_calls_results
            tool_calls_results.append({
                "result": result,
                "function_name": function_name,
                "function_args": function_args
            })
            
            # Always add a tool message response for each tool call
            messages.append({
                "role": "tool",
                "name": function_name,
                "content": json.dumps(result),
                "tool_call_id": tool_call_id  # Use the stored tool call ID
            })

        # Get next response with tool results
        response = openai_client.chat.completions.create(
            model=SYS_PROMPT["model"],
            messages=messages,
            tools=tools,
            tool_choice="auto",  # Allow more tool calls if needed
        )
        token_ussage={"model":"gpt-4.1", **response.usage.model_dump()}
        # final_response = response.choices[0].message.content
        # messages.append(response.choices[0].message)
    # Final response without tools to summarize all findings
    messages.append({"role": "user", "content": req.message})
    messages.append({"role": "assistant", "content": response.choices[0].message.content})

    # if isinstance(identify_result, dict):
    #     # Tool might have already parsed the output
    #     suggested_products = identify_result.get("products", [])
    #     print("suggested_products (dict):", suggested_products)

    # else:
    #     # Assume it's a CallToolResult (from LangChain/OpenAI function call)
    #     try:
    #         json_str = identify_result.content[0].text
    #         data = json.loads(json_str)
    #         print("ddaaaaaaa",data)
    #         suggested_products = data.get("suggested_products", [])
    #         print("suggested_products (tool):", suggested_products)
    #     except Exception as e:
    #         print("Error parsing tool output:", str(e))
    #         suggested_products = []
    # print("Final suggested products:", suggested_products)

    return messages[-1]["content"], {
        "source_nodes": source_nodes,
        "tool_calls": tool_calls,
        "tool_calls_results": tool_calls_results,
        "identified_product": identified_product,
        "suggested_product":suggested_products,
        "language": language,
        "token_usage": {
            "input_tokens": original_usage,
            "output_tokens": token_ussage,
            "lang_usage": lang_usage,
            "identify_prod_usage": prod_usage
        },
        "image_process_cost": req.image_process_metadata
    }
