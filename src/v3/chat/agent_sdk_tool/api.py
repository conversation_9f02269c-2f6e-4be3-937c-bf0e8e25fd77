# Standard Library Imports
import asyncio
import sys
import time
from typing import Literal, Optional, List

# Third Party Imports
from agents import (
    Agent,
    GuardrailFunctionOutput,
    InputGuardrailTripwireTriggered,
    ModelSettings,
    OutputGuardrailTripwireTriggered,
    RunConfig,
    Run<PERSON>ontextWrapper,
    Runner,
    TResponseInputItem,
    WebSearchTool,
    function_tool,
    input_guardrail,
    output_guardrail,
)
from agents.extensions.handoff_prompt import RECOMMENDED_PROMPT_PREFIX
from openai import AsyncOpenAI
from openai.types.responses import (
    ResponseOutputMessage,
    ResponseTextDeltaEvent,
)
from pydantic import BaseModel, Field
import os
from dotenv import load_dotenv
from pymongo import MongoClient

# FastAPI specific imports
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse

load_dotenv()

# ------------------------------------------------------------------------------------------------
# Constants
# ------------------------------------------------------------------------------------------------

WORKFLOW_NAME = "Breast Surgery Query Workflow"
GROUP_ID = "conversation-1"
USER_ID = "123" # In a real app, this would come from the authenticated user

api_key_from_env = os.getenv("OPENAI_API_KEY")
if not api_key_from_env:
    raise ValueError("OPENAI_API_KEY environment variable not set. Please check your .env file.")
client_openai = AsyncOpenAI(api_key=api_key_from_env)

# MongoDB Setup
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
MONGO_DB_NAME = os.getenv("MONGO_DB_NAME", "clinic_bookings")
MONGO_COLLECTION_NAME = os.getenv("MONGO_COLLECTION_NAME", "bookings")

mongo_client: MongoClient = None
db = None
bookings_collection = None

# ------------------------------------------------------------------------------------------------
# Response Schema
# ------------------------------------------------------------------------------------------------

class MessageOutput(BaseModel):
    response: str

class MessageOutputWithCoT(BaseModel):
    reasoning: Optional[str] = None
    response: Optional[str] = None

class UrgencyInput(BaseModel):
    urgency: Literal["urgent", "not urgent"]

class RelevanceInputGuardrailOutput(BaseModel):
    reasoning: str = Field(description="Use this as a scratchpad to reflect for whether the input is relevant to Breast implant. and booking a appointment")
    is_irrelevant: bool = Field(description="Your final answer.")
    error_message: Literal["Input is relevant", "Input is irrelevant"] = Field(description="If the input is relevant to breast implant, return 'Input is relevant'. If the input is not relevant to breast implant, return 'Input is irrelevant'.")

class ModerationInputGuardrailOutput(BaseModel):
    is_flagged: bool = Field(description="Your final answer.")
    error_message: Literal["Input is not flagged", "Input is flagged"] = Field(description="If the input is not flagged, return 'Input is not flagged'. If the input is flagged, return 'Input is flagged'.")

# --- Pydantic Models for Booking ---
class ClientDetails(BaseModel):
    name: Optional[str] = Field(description="The client's full name.")
    contact: Optional[str] = Field(description="The client's contact information (e.g., phone number, email address).")
    medical_history: Optional[str] = Field(description="Brief summary of past medical history provided by the client (e.g., 'none', 'diabetes', 'cancer').")

class BookingConfirmation(BaseModel):
    client_details: ClientDetails = Field(description="Details of the client making the booking, including name, contact, and medical history.")
    booking_intent_summary: str = Field(description="A concise summary of the client's booking intent.")
    timestamp: float = Field(default_factory=time.time, description="Timestamp of when the booking intent was captured.")

# --- FastAPI Request/Response Models ---
class QueryRequest(BaseModel):
    question: str

class QueryResponse(BaseModel):
    response: str

# ------------------------------------------------------------------------------------------------
# Guardrail Agents
# ------------------------------------------------------------------------------------------------

input_guardrail_agent = Agent(
    name="Guardrail check",
    model="gpt-4o-mini-2024-07-18",
    instructions=(
        "Check if the user's query is related to medical appointments, procedures, "
        "consultations, or information about breast surgery services. "
        "Flag as irrelevant ONLY if clearly off-topic."
    ),
    output_type=RelevanceInputGuardrailOutput,
)

@input_guardrail
async def relevance_guardrail(
    ctx: RunContextWrapper[None], agent: Agent, input: str | list[TResponseInputItem]
) -> GuardrailFunctionOutput:
    result = await Runner.run(input_guardrail_agent, input, context=ctx.context)
    return GuardrailFunctionOutput(
        output_info=result.final_output,
        tripwire_triggered=result.final_output.is_irrelevant,
    )

@input_guardrail
async def moderation_guardrail(
    ctx: RunContextWrapper[None], agent: Agent, input: str | list[TResponseInputItem]
) -> GuardrailFunctionOutput:
    latest_user_message = ""
    if isinstance(input, str):
        latest_user_message = input
    elif isinstance(input, list):
        for message_item in reversed(input):
            if isinstance(message_item, dict) and message_item.get('role') == 'user':
                latest_user_message = message_item.get('content', '')
                break
            elif hasattr(message_item, 'role') and message_item.role == 'user':
                latest_user_message = message_item.content
                break

    if not latest_user_message:
        return GuardrailFunctionOutput(output_info=ModerationInputGuardrailOutput(is_flagged=False, error_message="Input is not flagged"), tripwire_triggered=False)

    response = await client_openai.moderations.create(
        model="omni-moderation-2024-09-26",
        input=latest_user_message,
    )
    flagged = response.results[0].flagged

    if flagged:
        return GuardrailFunctionOutput(
            output_info=ModerationInputGuardrailOutput(is_flagged=flagged, error_message="Input is flagged"),
            tripwire_triggered=flagged,
        )
    return GuardrailFunctionOutput(output_info=ModerationInputGuardrailOutput(is_flagged=flagged, error_message="Input is not flagged"), tripwire_triggered=flagged)

# ------------------------------------------------------------------------------------------------
# Tools and Agents
# ------------------------------------------------------------------------------------------------

@function_tool
async def save_booking_to_db(name: str, contact: str, medical_history: str, booking_intent_summary: str) -> str:
    """Saves booking details to MongoDB. Only call when all required fields are present.

    Args:
        name: Full name of the client
        contact: Phone number or email address
        medical_history: Brief summary of relevant medical history
        booking_intent_summary: Brief summary of the booking intent

    Returns:
        Success or error message
    """
    try:
        # Simple test first - just try to save a basic document
        if bookings_collection is None:
            return "ERROR: MongoDB collection not available"

        # Create a simple document to test
        simple_doc = {
            "client_name": name,
            "client_contact": contact,
            "client_medical_history": medical_history,
            "booking_summary": booking_intent_summary,
            "timestamp": time.time(),
            "status": "pending"
        }

        # Try to insert the document
        result = bookings_collection.insert_one(simple_doc)

        if result.inserted_id:
            return f"SUCCESS: Booking saved with ID {result.inserted_id}. A specialist will contact you shortly."
        else:
            return "ERROR: Failed to save booking - no ID returned"

    except Exception as e:
        error_msg = f"ERROR: Database operation failed - {str(e)}"
        return error_msg

booking_agent = Agent(
    name="Booking Specialist",
    model="gpt-4o-mini-2024-07-18",
    instructions=(
        "ROLE: Booking Intent Capture System\n\n"
        "YOUR ONLY JOB: Capture name, contact, and medical history\n\n"
        "WHEN USER MESSAGE RECEIVED:\n"
        "1. IMMEDIATELY extract these fields if present:\n"
        "   - name: any name mentioned\n"
        "   - contact: any phone/email mentioned\n"
        "   - medical_history: any history mentioned (or 'none'/'no previous history')\n\n"
        "2. IF ALL 3 FIELDS FOUND:\n"
        "   → IMMEDIATELY call save_booking_to_db with extracted data\n"
        "   → Return ONLY: 'Booking intent captured. A specialist will contact you shortly.'\n\n"
        "3. IF ANY FIELDS MISSING:\n"
        "   → Ask ONLY for missing fields\n"
        "   → Format: 'Please provide: [field1], [field2]'\n\n"
        "❌ NEVER ask for date/time\n"
        "❌ NEVER ask for service type\n"
        "❌ NEVER ask for confirmation\n"
        "❌ NEVER add pleasantries"
    ),
    tools=[save_booking_to_db],
    model_settings=ModelSettings(
        tool_choice="auto",
        temperature=0.0,
        top_p=0.1,
        max_tokens=200
    ),
    output_type=MessageOutputWithCoT
)

dramit_agent = Agent(
    name="Breast Surgery Specialist",
    model="gpt-4o-mini-2024-07-18",
    model_settings=ModelSettings(tool_choice='auto'),
    instructions=f"{RECOMMENDED_PROMPT_PREFIX} Provide information about breast surgery procedures, costs, and clinic details.",
    tools=[WebSearchTool()],
    output_type=MessageOutputWithCoT,
)

reply_agent = Agent(
    name="Reply Agent",
    model="gpt-4o-mini-2024-07-18",
    instructions=f"{RECOMMENDED_PROMPT_PREFIX} Format responses professionally.",
    output_type=MessageOutput,
)

# Update the query_router_agent tools section to include descriptions:
query_router_agent = Agent(
    name="Query Router",
    model="gpt-4o-mini-2024-07-18",
    instructions=(
        "ROLE: Strict Query Router - DO NOT MODIFY REQUESTS\n\n"
        "PROTOCOL:\n"
        "1. IF message contains booking-related words ('book', 'appointment', 'schedule'):\n"
        "   - PASS RAW INPUT DIRECTLY to booking specialist\n"
        "   - DO NOT ADD/REMOVE/CHANGE ANYTHING\n"
        "2. ELSE:\n"
        "   - Pass to breast surgery specialist\n"
        "3. FINAL OUTPUT always goes to reply agent\n\n"
        "ABSOLUTE RULES:\n"
        "❌ NEVER modify booking requests\n"
        "❌ NEVER add questions/fields\n"
        "❌ NEVER intercept booking specialist's workflow\n"
        "❌ NEVER pre-process user messages"
    ),
    tools=[
        booking_agent.as_tool(
            tool_name="consult_booking_specialist",
            tool_description="ONLY for raw booking requests. DO NOT MODIFY INPUT."
        ),
        dramit_agent.as_tool(
            tool_name="consult_breast_surgery_specialist",
            tool_description="For non-booking information requests only."
        )
    ],
    output_type=MessageOutput,
    handoffs=[reply_agent],
    # input_guardrails=[relevance_guardrail, moderation_guardrail],/
)

# ------------------------------------------------------------------------------------------------
# FastAPI Application
# ------------------------------------------------------------------------------------------------

app = FastAPI(
    title="Breast Surgery Clinic Agent API",
    description="API for routing queries and managing breast surgery clinic interactions.",
    version="1.0.0",
)

@app.on_event("startup")
async def startup_db_client():
    global mongo_client, db, bookings_collection
    try:
        mongo_client = MongoClient(MONGO_URI)
        db = mongo_client[MONGO_DB_NAME]
        bookings_collection = db[MONGO_COLLECTION_NAME]
        print(f"Connected to MongoDB: {MONGO_DB_NAME}.{MONGO_COLLECTION_NAME}")
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")

@app.on_event("shutdown")
async def shutdown_db_client():
    if mongo_client:
        mongo_client.close()
        print("MongoDB connection closed.")

@app.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    question = request.question
    final_response_content = "I'm sorry, I couldn't process your request. Please try again."

    try:
        print(f"\n🔍 Received query: {question}")
        print(f"📊 MongoDB connected: {bookings_collection is not None}")

        # Check if this is a booking request
        is_booking_request = any(word in question.lower() for word in ['book', 'appointment', 'schedule'])
        print(f"🎯 Is booking request: {is_booking_request}")

        # Let the router agent handle all routing decisions
        result = Runner.run_streamed(
            starting_agent=query_router_agent,
            input=question,
            run_config=RunConfig(
                workflow_name=WORKFLOW_NAME,
                group_id=GROUP_ID,
                trace_metadata={"user_id": USER_ID},
            ),
        )

        print(f"🚀 Starting workflow with router agent")

        async for event in result.stream_events():
            print(f"📡 Event: {event.type}")

            if event.type == "raw_response_event":
                event_data = event.data
                if isinstance(event_data, ResponseTextDeltaEvent):
                    if event_data.delta:
                        print(f"📝 Text delta: {event_data.delta}")
                        final_response_content = event_data.delta
                elif isinstance(event_data, ResponseOutputMessage):
                    print(f"📄 Output message: {event_data.content}")
                    final_response_content = event_data.content
            elif event.type == "run_item_stream_event":
                print(f"🔧 Tool event: {event.name}")
                if event.name == "tool_output":
                    print(f"🛠️ Tool output: {event.item.output}")
                    # Prioritize booking-specific responses
                    if "Please provide:" in event.item.output or "Booking intent captured" in event.item.output:
                        print(f"✅ Returning booking response: {event.item.output}")
                        return QueryResponse(response=event.item.output)

        print(f"🏁 Final result type: {type(result.final_output)}")
        if hasattr(result.final_output, 'response') and result.final_output.response:
            final_response_content = result.final_output.response
            print(f"📋 Final response from result: {final_response_content}")
        elif hasattr(result.final_output, 'error_message'):
            final_response_content = result.final_output.error_message
            print(f"❌ Error message: {final_response_content}")

        print(f"🎯 Returning final response: {final_response_content}")
        return QueryResponse(response=final_response_content)

    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(500, detail="Internal server error")

@app.get("/")
async def root():
    return {"message": "Agent API is running!"}

@app.get("/bookings")
async def get_bookings():
    """Get all bookings from the database"""
    try:
        if bookings_collection is None:
            return {"error": "Database not connected"}

        # Get all bookings, sorted by timestamp (newest first)
        bookings = list(bookings_collection.find().sort("timestamp", -1).limit(10))

        # Convert ObjectId to string for JSON serialization
        for booking in bookings:
            booking["_id"] = str(booking["_id"])

        return {
            "total_bookings": bookings_collection.count_documents({}),
            "recent_bookings": bookings
        }
    except Exception as e:
        return {"error": f"Failed to retrieve bookings: {str(e)}"}

@app.get("/bookings/count")
async def get_bookings_count():
    """Get the total count of bookings"""
    try:
        if bookings_collection is None:
            return {"error": "Database not connected"}

        count = bookings_collection.count_documents({})
        return {"total_bookings": count}
    except Exception as e:
        return {"error": f"Failed to count bookings: {str(e)}"}
