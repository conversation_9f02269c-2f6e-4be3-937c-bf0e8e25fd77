import json
import requests
from bson import ObjectId
import datetime
# from .track_time import time_it
from src.v2.dashboard.cta.routes import create_cta, CTACreate
import asyncio
from typing import Optional, Union

from src.v2.dashboard.cta.models import CTAType

# @time_it
def create_issue_tickets(user_req, issue_type:Optional[str],channel,current_user, **kwargs):
    """
    Create a dynamic issue ticket with all fields passed via kwargs.

    Expected Keys in kwargs:
        - name: Ticket title
        - issue_type: ticket | booking
        - description: Description
        - channel (optional)
        - tenant_name (optional)
        - Any other dynamic fields (age, gender, etc.) are stored in extra_info
    """
    if issue_type is None:
        issue_type = kwargs.pop("issue_type", "ticket")
    else:
        # Remove issue_type from kwargs if present to avoid duplication/conflict
        kwargs.pop("issue_type", None)
    name = issue_type + " Acknowledge"

    description = kwargs.pop("description", "")
    channel = kwargs.pop("channel", None)
    tenant_name = kwargs.pop("tenant_name", None)


    # Get customer info
    customer_info = current_user.db.customers.find_one(
        {"customer_id": user_req.customer_id},
        {
            "_id": 0, "customer_name": 1, "whatsapp_id": 1,
            "whatsapp_number": 1, "email": 1, "phone_number": 1,
            "country_code": 1
        }
    ) or {}

    # Remaining kwargs are treated as dynamic additional fields
    extra_info = {**customer_info, **kwargs}

    # Normalize issue type
    valid_cta_type = CTAType.TICKET.value  # Default
    if issue_type:
        normalized_type = str(issue_type).lower().strip()
        if normalized_type == "booking":
            valid_cta_type = CTAType.BOOKING.value
        elif normalized_type != "ticket":
            print(f"Warning: Invalid issue_type '{issue_type}', defaulting to 'ticket'")

    # Resolve tenant name
    tenant_name = tenant_name or getattr(current_user, 'tenant_id', 'default')

    print("tenant_name", tenant_name)

    # Custom titles
    title_map = {
        ("ticket", "dramit"): "Medical Issue Ticket",
        ("ticket", "dramit-dev"): "Medical Issue Ticket",
        ("ticket", "techspire"): "Academic Issue Ticket",
        ("booking", "dramit"): "Medical Appointment Booking",
        ("booking", "dramit-dev"): "Medical Appointment Booking",
        ("booking", "techspire"): "Academic Consultation Booking"
    }
    ticket_title = title_map.get((valid_cta_type, tenant_name), f"{valid_cta_type.title()} Acknowledgement")

    print(f"Creating {valid_cta_type} with extra fields: {extra_info}")

    # Final CTA creation
    resp_ = asyncio.run(create_cta(
        CTACreate(
            name=name or ticket_title,
            customer_name=customer_info.get("customer_name", "Anonymous"),
            type=valid_cta_type,
            description=description,
            channel = kwargs.pop("channel", None) or getattr(user_req, "channel")
,
            customer_id=user_req.customer_id,
            extra_info=extra_info
        ),
        current_user
    ))

    return {
        "success": resp_.success,
        "cta_id": str(resp_.data.id),
        "message": resp_.message or "CTA created successfully",
        "error": resp_.error or None
    }

def handle_booking(**kwargs):
    # print("kwargsss",kwargs)
    kwargs_copy = kwargs.copy()
    user_req = kwargs.pop("user_req", None)
    current_user = kwargs.pop("current_user", None)

    kwargs.pop("issue_type", None)  # Remove if passed, we handle it ourselves
    kwargs.pop("channel", None)  # Add this line before the function call


    # Direct required fields (no aliases)
    # name = kwargs_copy.pop("Name", None)
    # contact = kwargs_copy.pop("Contact Number", None)

    # if not name or not contact:
        # raise ValueError("Missing required fields: 'Name' and/or 'Contact Number'")

    tenant_name = getattr(current_user, 'slug', None) or getattr(current_user, 'tenant_id', 'default')

    tenant_config = {
        "dramit": {"context": "medical", "subject": "patient", "service": "appointment"},
        "dramit-dev": {"context": "medical", "subject": "patient", "service": "appointment"},
        "techspire": {"context": "academic", "subject": "student", "service": "consultation"}
    }

    config = tenant_config.get(tenant_name, {"context": "", "subject": "customer", "service": "booking"})

    # Titles & Labels
    # booking_title = f"{config['context'].title()} {config['service'].title()} Booking" if config['context'] else "Booking Acknowledgement"
    # field_label = f"{config['context'].title()} History" if config['context'] else "Additional Information"
    # acknowledgment_msg = f"{config['context'].title()} {config['service']} booking for {config['subject']} {name} with contact {contact} has been acknowledged."

    # Dynamic description
    # description_parts = []
    # if 'description' in kwargs:
    #     description_parts.append(kwargs.pop('description'))
    # if 'customer_age' in kwargs:
    #     description_parts.append(f"Age: {kwargs.pop('customer_age')}")
    # if 'customer_medical_history' in kwargs:
    #     description_parts.append(f"{field_label}: {kwargs.pop('customer_medical_history')}")
    # description_parts.append(acknowledgment_msg)
    # full_description = "\n".join(description_parts)

    # Final call to create_issue_tickets
    data = create_issue_tickets(
        # name=booking_title,
        issue_type="booking",
        # description=full_description,
        user_req=user_req,
        current_user=current_user,
        channel=getattr(user_req, 'channel', None),
        tenant_name=tenant_name,
        # customer_name=name,
        # customer_phone_no=contact,
        **kwargs  # rest of the dynamic fields
    )

    return {
        # "message": acknowledgment_msg,
        "status": "success",
        "booking_type": f"{config['context']} {config['service']}".strip(),
        "tenant": tenant_name
    }, data

def initial_address_information(db):
    """Initial Address Information for new conversations"""
    INITIAL_ADDRESS_PROMPT = db.prompt.find_one({"name": "initial_address_information"}, {"_id": 0, "name": 0})

    org_detail = db.business_info.find_one({"name": "BusinessInfo"}, {"_id": 0}) or {}

    # Fallbacks for missing keys
    greeting_text = INITIAL_ADDRESS_PROMPT.get("text", "").format(
        org_name=org_detail.get("org_name", ""),
        agent_name=org_detail.get("agent_name", "AI Bot"),
        org_description=org_detail.get("org_description", "")
    )

    return {"initial_greeting": greeting_text}

