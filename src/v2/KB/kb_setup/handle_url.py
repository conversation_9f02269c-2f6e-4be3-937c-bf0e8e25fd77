import re
import asyncio
import httpx
import pdfkit
from selectolax.parser import HTMLParser
from fastapi import UploadFile
from io import BytesIO
import traceback
import re

def safe_filename(title: str, ext: str = "pdf", max_words: int = 10, max_chars: int = 150) -> str:
    # Lowercase and normalize
    title = title.lower().replace("/", " ").replace("|", " ").replace("-", " ").replace("_", " ")
    # Extract words and limit
    words = re.findall(r'\w+', title)
    short_name = "_".join(words[:max_words])
    # Remove bad characters and truncate
    short_name = re.sub(r'[^a-zA-Z0-9_\-]', '', short_name)
    short_name = short_name[:max_chars]
    # Fallback if name is empty
    if not short_name:
        short_name = "document"
    return f"{short_name}.{ext}"


async def get_filename(title: str, ext: str = "pdf") -> str:
    # Lowercase and replace spaces/slashes/pipes with underscores
    title = title.lower().replace(" ", "_").replace("/", "_").replace("|", "_")
    words = re.findall(r'\w+', title)
    short_title = "_".join(words[:10])
    filename = re.sub(r'[^a-zA-Z0-9_\-]+', '', short_title)
    if not filename:
        filename = "document"
    # Truncate filename to max 150 chars
    filename = filename[:150]
    return f"{filename}.{ext}"



async def generate_pdf_from_html(html_content: str) -> bytes:
    """
    Fast PDF generation with optimized settings for speed and readability.
    """
    # Streamlined options for speed while maintaining quality
    options = {
        'page-size': 'A4',
        'margin-top': '0.8in',
        'margin-right': '0.6in',
        'margin-bottom': '0.8in',
        'margin-left': '0.6in',
        'encoding': "UTF-8",
        'enable-local-file-access': None,
        'disable-smart-shrinking': None,
        'minimum-font-size': '12',
        'dpi': 150,  # Reduced for speed
        'image-quality': 85,  # Reduced for speed
        'load-error-handling': 'ignore',
        'load-media-error-handling': 'ignore',
        'disable-javascript': None,  # Disable JS for speed
        'quiet': None
    }

    try:
        # Run pdfkit in a thread to avoid blocking
        pdf_bytes = await asyncio.to_thread(
            pdfkit.from_string,
            html_content,
            False,
            options=options
        )
        return pdf_bytes
    except Exception as e:
        print(f"PDF generation failed: {e}, trying minimal options...")
        # Ultra-minimal fallback for maximum compatibility
        minimal_options = {
            'page-size': 'A4',
            'encoding': "UTF-8",
            'quiet': None
        }
        pdf_bytes = await asyncio.to_thread(
            pdfkit.from_string,
            html_content,
            False,
            options=minimal_options
        )
        return pdf_bytes

async def save_pdf(url: str, filename: str = None) -> UploadFile:
    """
    Generate PDF from URL with lightweight JS simulation and enhanced content processing.
    """
    try:
        html_content = await fetch_with_js_simulation(url)
        tree = HTMLParser(html_content)
        title_element = tree.css_first('title')
        title = title_element.text() if title_element else "untitled"
        output_filename = await get_filename(filename or title)
        cleaned_html = await process_dynamic_content(tree, url)
        pdf_bytes = await generate_pdf_from_html(cleaned_html)
        final_filename = output_filename.replace('.pdf', '_website.pdf')
        print(f"PDF generated for {url}")
        return UploadFile(
            filename=final_filename,
            file=BytesIO(pdf_bytes),
            headers={"Content-Type": "application/pdf"}
        )
    except Exception as e:
        print(f"Error generating PDF from {url}: {e}")
        traceback.print_exc()
        raise

async def fetch_with_js_simulation(url: str) -> str:
    """
    Fast HTML fetching with lightweight JavaScript content extraction.
    Uses pattern recognition instead of actual JS execution for speed.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    async with httpx.AsyncClient(
        timeout=httpx.Timeout(400.0),  # Reduced timeout for speed
        headers=headers,
        follow_redirects=True
    ) as client:
        # Get the initial HTML quickly
        response = await client.get(url)
        html_content = response.text

        # Fast pattern-based content extraction
        tree = HTMLParser(html_content)

        # Quick content revelation without heavy processing
        reveal_hidden_content_fast(tree)

        return tree.html


def reveal_hidden_content_fast(tree: HTMLParser):
    """
    Ultra-fast content revelation using pattern matching.
    No async operations, just direct DOM manipulation.
    """
    # Reveal lazy-loaded images instantly
    for img in tree.css('img[data-src], img[data-lazy], img[data-original]'):
        data_src = (img.attributes.get('data-src') or
                   img.attributes.get('data-lazy') or
                   img.attributes.get('data-original'))
        if data_src and img.attributes:
            img.attributes['src'] = data_src

    # Remove all hiding styles and classes in one pass
    hiding_patterns = [
        'display:none', 'display: none', 'visibility:hidden', 'visibility: hidden',
        'opacity:0', 'opacity: 0'
    ]

    for element in tree.css('*'):
        if not element.attributes:
            continue

        # Fix styles
        if 'style' in element.attributes and element.attributes['style']:
            style = element.attributes['style']
            for pattern in hiding_patterns:
                style = style.replace(pattern, '')
            element.attributes['style'] = style

        # Fix classes - remove common hiding classes
        if 'class' in element.attributes and element.attributes['class']:
            classes = element.attributes['class'].split()
            visible_classes = []
            for cls in classes:
                if not any(hide in cls.lower() for hide in ['hidden', 'invisible', 'd-none', 'fade', 'collapse']):
                    visible_classes.append(cls)
                # Add show classes for Bootstrap/common frameworks
                if cls in ['collapse', 'fade']:
                    visible_classes.append('show')
            element.attributes['class'] = ' '.join(visible_classes)


async def process_dynamic_content(tree: HTMLParser, url: str = None) -> str:
    """
    Fast dynamic content processing optimized for speed.
    Minimal operations, maximum content extraction.
    """
    # Quick cleanup - remove only the most problematic elements
    for element in tree.css('script, noscript, style'):
        element.decompose()

    # Fast navigation cleanup - single pass
    for element in tree.css('nav, header, footer, .navbar, .nav, .menu, .sidebar, .breadcrumb, .footer'):
        element.decompose()

    # Ultra-fast slider/carousel handling - batch process
    slider_elements = tree.css('.slick-slider, .swiper-container, .owl-carousel, .carousel, .slider, .slideshow')
    for container in slider_elements:
        # Make all child slides visible in one operation
        slides = container.css('.slick-slide, .swiper-slide, .owl-item, .carousel-item, .slide')
        for slide in slides:
            if slide.attributes:
                # Quick class cleanup
                if 'class' in slide.attributes:
                    classes = slide.attributes['class'].replace('hidden', '').replace('inactive', '').replace('fade', '')
                    slide.attributes['class'] = classes + ' show'

                # Quick style cleanup
                if 'style' in slide.attributes:
                    style = slide.attributes['style']
                    style = style.replace('display:none', 'display:block').replace('visibility:hidden', 'visibility:visible')
                    slide.attributes['style'] = style

    # Fast tab/accordion handling
    for element in tree.css('.tab-content, .accordion-content, .collapse, .panel-collapse'):
        if element.attributes and 'class' in element.attributes:
            element.attributes['class'] = element.attributes['class'].replace('collapse', 'show').replace('fade', 'show')

    # Remove problematic popups quickly
    for element in tree.css('.modal, .popup, .overlay, .lightbox'):
        element.decompose()

    # Get the processed HTML and add lightweight CSS
    html_content = tree.html

    # Minimal, fast-loading CSS for PDF
    lightweight_css = """
    <style>
    @page { margin: 0.8in; size: A4; }
    body { font-family: Arial, sans-serif; font-size: 12px; line-height: 1.6; color: #000; margin: 0; padding: 15px; }
    h1, h2, h3, h4, h5, h6 { color: #000; margin: 15px 0 8px 0; font-weight: bold; page-break-after: avoid; }
    h1 { font-size: 18px; } h2 { font-size: 16px; } h3 { font-size: 14px; }
    p { margin-bottom: 8px; }
    img { max-width: 100%; height: auto; display: block; margin: 8px auto; }
    table { border-collapse: collapse; width: 100%; margin: 8px 0; font-size: 10px; }
    th, td { border: 1px solid #000; padding: 4px; text-align: left; }
    th { background-color: #f0f0f0; font-weight: bold; }
    ul, ol { margin: 8px 0; padding-left: 20px; }
    li { margin-bottom: 3px; }
    a { color: #000; text-decoration: underline; }

    /* Force visibility */
    .hidden, .d-none, .invisible, .collapse, .fade { display: block !important; visibility: visible !important; opacity: 1 !important; }
    .slick-slide, .swiper-slide, .owl-item, .carousel-item, .slide { display: block !important; opacity: 1 !important; position: static !important; margin-bottom: 10px; border: 1px solid #eee; padding: 8px; }
    .tab-content, .accordion-content { display: block !important; margin-bottom: 10px; }
    .advertisement, .ads, .popup, .modal, .overlay { display: none !important; }
    .container, .row, .col, [class*="col-"] { width: 100% !important; display: block !important; }
    </style>
    """

    # Insert CSS into head
    if '<head>' in html_content:
        html_content = html_content.replace('<head>', f'<head>{lightweight_css}')
    elif '<html>' in html_content:
        html_content = html_content.replace('<html>', f'<html><head>{lightweight_css}</head>')
    else:
        html_content = f'<html><head>{lightweight_css}</head><body>{html_content}</body></html>'

    return html_content


async def clean_html_for_pdf(tree: HTMLParser) -> str:
    """
    Clean HTML content for better PDF generation.
    Removes problematic elements while preserving content structure.
    """
    # Remove scripts, styles, and other non-content elements
    for element in tree.css('script, style, noscript'):
        element.decompose()

    # Remove navigation and header elements that might interfere with PDF layout
    nav_selectors = [
        'nav', 'header', '.navbar', '.nav', '.navigation',
        '.header', '.top-bar', '.menu', '.sidebar'
    ]
    for selector in nav_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Remove footer elements
    for element in tree.css('footer, .footer, .copyright'):
        element.decompose()

    # Remove modal and popup elements
    modal_selectors = [
        '.modal', '.popup', '.overlay', '[role="dialog"]',
        '[aria-modal="true"]', '.lightbox'
    ]
    for selector in modal_selectors:
        for element in tree.css(selector):
            element.decompose()

    # Fix slider/carousel elements for PDF (make all slides visible)
    slider_selectors = [
        '.slick-slider', '.swiper-container', '.owl-carousel', '.carousel'
    ]
    for selector in slider_selectors:
        for container in tree.css(selector):
            # Remove slider-specific classes that hide content
            if container.attributes:
                container.attributes.pop('class', None)

            # Make all slides visible
            slides = container.css('.slick-slide, .swiper-slide, .owl-item, .carousel-item')
            for slide in slides:
                if slide.attributes:
                    slide.attributes.pop('style', None)
                    slide.attributes.pop('class', None)

    # Remove problematic attributes that can break PDF generation
    for element in tree.css('*'):
        if element.attributes:
            # Remove event handlers and data attributes
            attrs_to_remove = []
            for attr in element.attributes:
                if attr.startswith(('on', 'data-')):
                    attrs_to_remove.append(attr)

            for attr in attrs_to_remove:
                element.attributes.pop(attr, None)

    # Get the HTML and add enhanced CSS for better PDF formatting and readability
    html_content = tree.html

    # Enhanced CSS for maximum readability in PDFs
    css_style = """
    <style>
    @page {
        margin: 1in;
        size: A4;
    }

    body {
        font-family: 'Times New Roman', Times, serif;
        font-size: 14px;
        line-height: 1.8;
        color: #000000;
        background-color: #ffffff;
        margin: 0;
        padding: 20px;
        text-align: justify;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: Arial, sans-serif;
        color: #000000;
        margin-top: 24px;
        margin-bottom: 12px;
        page-break-after: avoid;
    }

    h1 { font-size: 24px; font-weight: bold; }
    h2 { font-size: 20px; font-weight: bold; }
    h3 { font-size: 18px; font-weight: bold; }
    h4 { font-size: 16px; font-weight: bold; }

    p {
        margin-bottom: 12px;
        text-indent: 0;
        orphans: 3;
        widows: 3;
    }

    img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 12px auto;
        page-break-inside: avoid;
    }

    table {
        border-collapse: collapse;
        width: 100%;
        margin: 12px 0;
        font-size: 12px;
        page-break-inside: avoid;
    }

    th, td {
        border: 1px solid #000000;
        padding: 8px;
        text-align: left;
        vertical-align: top;
    }

    th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    ul, ol {
        margin: 12px 0;
        padding-left: 30px;
    }

    li {
        margin-bottom: 6px;
    }

    a {
        color: #000000;
        text-decoration: underline;
    }

    blockquote {
        margin: 12px 0;
        padding: 12px;
        border-left: 4px solid #cccccc;
        background-color: #f9f9f9;
        font-style: italic;
    }

    code, pre {
        font-family: 'Courier New', monospace;
        background-color: #f5f5f5;
        padding: 4px;
        border: 1px solid #cccccc;
    }

    pre {
        padding: 12px;
        margin: 12px 0;
        white-space: pre-wrap;
        page-break-inside: avoid;
    }

    /* Ensure slider content is visible */
    .slick-slide, .swiper-slide, .owl-item, .carousel-item {
        display: block !important;
        opacity: 1 !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        transform: none !important;
    }

    /* Hide problematic elements */
    .advertisement, .ads, .popup, .modal, .overlay {
        display: none !important;
    }

    /* Page break controls */
    .page-break {
        page-break-before: always;
    }

    .no-break {
        page-break-inside: avoid;
    }
    </style>
    """

    # Insert CSS into head using string manipulation
    if '<head>' in html_content:
        html_content = html_content.replace('<head>', f'<head>{css_style}')
    elif '<html>' in html_content:
        html_content = html_content.replace('<html>', f'<html><head>{css_style}</head>')
    else:
        # If no html tag, wrap everything
        html_content = f'<html><head>{css_style}</head><body>{html_content}</body></html>'

    return html_content


async def handle_urls(urls: list) -> list[UploadFile]:
    """
    Process multiple URLs in parallel to generate PDFs.
    Optimized for speed with lightweight HTTP requests.
    """
    if not urls:
        return []

    tasks = []
    for url in urls:
        tasks.append(save_pdf(url))

    # Process all URLs concurrently for maximum speed
    pdfs = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out exceptions and return only successful PDFs
    successful_pdfs = []
    for i, result in enumerate(pdfs):
        if isinstance(result, Exception):
            print(f"Failed to process URL {urls[i]}: {result}")
        else:
            successful_pdfs.append(result)

    return successful_pdfs

if __name__ == "__main__":
    import asyncio
    asyncio.run(save_pdf("https://www.divinecosmeticsurgery.com/liposuction-surgery-cost.php"))