from datetime import datetime
from typing import Optional
from pydantic import BaseModel
from fastapi import HTTPException
from src.core.database import get_admin_db
class ProductDetail(BaseModel):
    name:str
    detail:str

class BusinessInfo(BaseModel):
    org_name:str
    org_type:str
    org_description:Optional[str]=None
    org_goal:Optional[str]=None
    org_contact:Optional[str]=None
    org_email:Optional[str]=None
    agent_name:Optional[str]="AI Bot"
    # agent_role:Optional[str]=None
    language:Optional[list[str]]=["English"]
    additional_agent_goal:Optional[str]=None
    agent_goal_type:Optional[str]=None

    set_up_complete:Optional[bool]=False

    def format_dummy_prompt(self, current_user, response_mode_str: str="Provide a detailed and explained answer in a couple of sentences."):
        simplified_prompt = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_simplified_setup"})

        if simplified_prompt:
            simplified_prompt.pop("_id", None)
            simplified_prompt["name"] = "reply_prompt_openai_simplified"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "reply_prompt_openai_simplified"},
                {"$set": simplified_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'reply_prompt_openai_simplified_setup' not found")


        standard_prompt = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_setup"})

        if standard_prompt:
            standard_prompt.pop("_id", None)
            standard_prompt["name"] = "reply_prompt_openai"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "reply_prompt_openai"},
                {"$set": standard_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'reply_prompt_openai_setup' not found")


        elaborated_prompt = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_elaborated_setup"})
        if elaborated_prompt:
            elaborated_prompt.pop("_id", None)
            elaborated_prompt["name"] = "reply_prompt_openai_elaborated"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "reply_prompt_openai_elaborated"},
                {"$set": elaborated_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'reply_prompt_openai_elaborated_setup' not found")
        initial_address_prompt=get_admin_db().prompt.find_one({"name": "initial_address_information_setup"})
        if initial_address_prompt:
            initial_address_prompt.pop("_id", None)
            initial_address_prompt["name"] = "initial_address_information"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "initial_address_information"},
                {"$set": initial_address_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'initial_address_information_setup' not found")

        return True
    @classmethod
    def _generate_description(cls, field_name: str) -> str:
        field_map = {
            "Contact Number": "Customer's contact number, with optional country code",
            "Address": "Full residential or business address",
            "Name": " Name of the customer (alphabetic characters only) ok with Firstname only too . dont force to have full name",
            "Age": "Age of the customer in years (between 1 and 120)",
            "Email": "Valid email address",
            "Gender": "Gender identity, allowed values: Male, Female,Other, M, F,O",
            "issue_type": "Assign the appropriate Type of issue (e.g., ticket, complaint, inquiry) to ticket/booking.generate one Not to get from user",
            "description": "Detailed description of the issue or request. This should be generated by the system,do not asked from the user."
    }
        return field_map.get(field_name, f"{field_name.replace('_', ' ').capitalize()} required for ticket creation")

    @classmethod
    def _generate_pattern(cls, field_name: str) -> str:
        pattern_map = {
            "Contact Number": r"^\+?[0-9]{7,15}$",
            "Address": r"^.{5,100}$",
            "Name": r"^[A-Za-z\s]{2,50}$",
            "Age": r"^(?:1[01][0-9]|120|[1-9]?[0-9])$",
            "Email": r"^[\w\.-]+@[\w\.-]+\.\w{2,}$",
            "Gender": r"^(Male|Female|Other|M|F|O)$",
            "issue_type": r"^[a-zA-Z_]+$",
            "description": r"^.{10,1000}$"
        }
        return pattern_map.get(field_name, ".*") 


    @classmethod
    def _update_ticket_tools(
        cls, 
        tools: list[str], 
        ticket_required_info: list[str] | None, 
        current_user
    ) -> list[str]:
        """Handle ticket tools updates, including required fields and their properties"""
        updated_tools = []

        print("ticket_toollss", tools)

        if not tools:
            return updated_tools

        tool_doc = current_user.db.tools.find_one({"name": "create_issue_tickets"})
        if not tool_doc:
            return updated_tools

        # Get the correct tool from the list
        tool = next((t for t in tool_doc["tools"] if t["function"]["name"] == "create_issue_tickets"), None)
        if not tool:
            return updated_tools

        function_def = tool["function"]
        parameters = function_def.get("parameters", {})
        existing_properties = parameters.get("properties", {})
        existing_required = set(parameters.get("required", []))

        # 🔐 Always preserve these
        default_required = {"description", "issue_type"}

        # 🔁 Merge user-provided + defaults
        final_required = list(default_required | set(ticket_required_info or []))

        # ✅ Generate fresh properties for each required field
        updated_properties = {}

        for field in final_required:
            updated_properties[field] = {
                "type": "string",
                "description": cls._generate_description(field),
                "pattern": cls._generate_pattern(field)
            }

            # 🔘 Add enum for issue_type
            if field == "issue_type":
                updated_properties[field]["enum"] = tools or []

        # ✅ Final MongoDB update payload
        update_fields = {
            "updated_at": datetime.now(),
            "tools.$[tool].function.parameters.required": final_required,
            "tools.$[tool].function.parameters.properties": updated_properties
        }

        # 🔍 For debug
        print("upppppp", update_fields)

        result = current_user.db.tools.update_one(
            {"name": "create_issue_tickets"},
            {"$set": update_fields},
            array_filters=[{
                "tool.type": "function",
                "tool.function.name": "create_issue_tickets"
            }]
        )

        print("Matched:", result.matched_count, "Modified:", result.modified_count)

        updated_tools.append("create_issue_tickets")
        return updated_tools
    @classmethod
    def _update_booking_tools(
        cls,
        booking: list[str],
        booking_required_info: list[str],
        current_user
    ) -> list[str]:
        """Handle booking tools updates: required fields + properties with pattern and description"""
        updated_tools = []

        if not booking or "handle_booking" not in booking:
            return updated_tools

        booking_doc = current_user.db.tools.find_one({"name": "handle_booking"})
        if not booking_doc:
            return updated_tools

        # Get the correct tool
        tool = next((t for t in booking_doc["tools"] if t["function"]["name"] == "handle_booking"), None)
        if not tool:
            return updated_tools

        parameters = tool["function"].get("parameters", {})
        existing_properties = parameters.get("properties", {})
        existing_required = set(parameters.get("required", []))

        # 🧼 Clean and deduplicate required list
        booking_required_info = list(set(booking_required_info or []))

        # ✅ Generate fresh properties for each required field
        updated_properties = {}

        for field in booking_required_info:
            updated_properties[field] = {
                "type": "string",
                "description": cls._generate_description(field),
                "pattern": cls._generate_pattern(field)
            }

        # 🔘 Always include `description` field if not explicitly listed
        if "description" not in booking_required_info:
            updated_properties["description"] = {
                "type": "string",
                "description": cls._generate_description("description"),
                "pattern": cls._generate_pattern("description")
            }

        final_required = list(set(booking_required_info + ["description"]))

        # Prepare the MongoDB update
        booking_update_fields = {
            "updated_at": datetime.now(),
            "tools.$[tool].function.parameters.required": final_required,
            "tools.$[tool].function.parameters.properties": updated_properties
        }

        # 🔍 Debug logging
        print("Booking update payload:", booking_update_fields)

        result = current_user.db.tools.update_one(
            {"name": "handle_booking"},
            {"$set": booking_update_fields},
            array_filters=[{
                "tool.type": "function",
                "tool.function.name": "handle_booking"
            }]
        )

        print("Matched:", result.matched_count, "Modified:", result.modified_count)

        updated_tools.append("handle_booking")
        return updated_tools


    @classmethod
    def update_prompt(
        cls, 
        prompt: str, 
        tools: list[str], 
        ticket_required_info: list[str] | None, 
        booking: list[str], 
        booking_required_info: list[str], 
        AI_greeting: bool, 
        current_user
    ):
        """Main method to update prompt and tools"""
        print("Received booking data:", booking)

        # Handle ticket tools
        ticket_tools = cls._update_ticket_tools(tools, ticket_required_info, current_user)
        
        # Handle booking tools
        booking_tools = cls._update_booking_tools(booking or [], booking_required_info, current_user)
        

        # Update business_info
        current_user.db.business_info.update_one(
            {"name": "BusinessInfo"},
            {
                "$set": {
                    "preferred_prompt": prompt,
                    "set_up_complete": True,
                    "used_tools": tools,
                    "ticket_required_info": ticket_required_info if ticket_required_info else [],
                    "booking_required_info": booking_required_info if booking_required_info else [],
                    "booking": booking if booking else [],
                    "AI_greeting": AI_greeting,
                    "updated_at": datetime.now()
                }
            },
            upsert=True
        )

        return {
            "status": "success tool and preferred prompt added"
        }